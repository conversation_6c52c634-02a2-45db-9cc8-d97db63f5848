# Alec Bahcheli

# run version (typically date)
VERSION='2025_08_21'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])

# data and results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
RES_DIR = "/".join([MAIN_DIR, "results", VERSION])


# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python'



###################################
# Complete workflow
###################################

rule all:
    input:


#####################
# sequencing and clinical overview 
#####################
include: "snakemake/001-financial_modeling.smk"

